
<?php
header('Content-Type: application/json');

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
    echo json_encode(['success' => false, 'message' => 'Sessão inválida']);
    exit;
}

include_once("config.php");

$reserva_id = $_POST['reserva_id'] ?? '';
$hospede_nome = $_POST['hospede_nome'] ?? '';

if (empty($reserva_id)) {
    echo json_encode(['success' => false, 'message' => 'ID da reserva não fornecido']);
    exit;
}

// Buscar dados da reserva
$sql = "SELECT r.*, h.id as hospede_id FROM reservas r
        LEFT JOIN hospedes h ON r.hospede_id = h.id
        WHERE r.id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $reserva_id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_object();

if (!$row) {
    echo json_encode(['success' => false, 'message' => 'Reserva não encontrada']);
    exit;
}

// Configurar variáveis para o formulário
$ajax_context = true;
$form_action = 'reservas_salvar.php';
$form_id = 'formEditarReserva';
$hidden_fields = '<input type="hidden" name="acao" value="editar">
                  <input type="hidden" name="reserva_id" value="' . htmlspecialchars($row->id) . '">
                  <input type="hidden" name="hospede_id" value="' . htmlspecialchars($row->hospede_id) . '">
                  <input type="hidden" name="hospede_nome" value="' . htmlspecialchars($hospede_nome) . '">';

// Preencher valores dos campos
$uh = $row->uh;
$numacomp = $row->numacomp;
$valor = $row->valor;
$dataentrada = $row->dataentrada;
$horaentrada = $row->horaentrada;
$datasaida = $row->datasaida;
$horasaida = $row->horasaida;
$acompanhantes = $row->acompanhantes;
$vemde = $row->vemde;
$vaipara = $row->vaipara;
$motivo = $row->motivo;
$transporte = $row->transporte;
$observacao = $row->observacao;
$titulo_reserva = 'Editar Reserva de:';
$button_text = 'Salvar Alterações';

// Capturar o HTML do formulário
ob_start();
include 'formulario_reserva.php';
$formulario_html = ob_get_clean();

// Adicionar elementos específicos para impressão
$print_elements = '
<div class="text-center mb-3 print-only">
    <img src="' . ($_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png') . '" alt="Logo" class="print-logo">
</div>

<div class="form-group no-print mt-3">
    <button type="button" class="btn btn-secondary" id="print-button-reserva">
        <i class="bi bi-printer"></i> Imprimir Reserva
    </button>
</div>

<center class="print-only">
    <br>
    <div id="qrcode-reserva"></div>
    <br>
    <p><small>QR Code do Hóspede: ' . htmlspecialchars($row->hospede_id) . '</small></p>
</center>';

$html = $formulario_html . $print_elements;

echo json_encode([
    'success' => true,
    'html' => $html,
    'hospede_id' => $row->hospede_id,
    'reserva_id' => $reserva_id
]);
?>